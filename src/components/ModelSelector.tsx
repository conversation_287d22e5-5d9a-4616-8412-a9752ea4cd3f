import React, { useState } from 'react';
import { Ch<PERSON><PERSON>Down, Cpu, Zap, Gauge, Search } from 'lucide-react';
import { GEMINI_MODELS } from '../config/models';
import { getModelById } from '../config/models';

interface ModelSelectorProps {
  selectedModel: string;
  onModelChange: (modelId: string) => void;
  disabled?: boolean;
  isGenerating?: boolean;
}

export const ModelSelector: React.FC<ModelSelectorProps> = ({
  selectedModel,
  onModelChange,
  disabled = false,
  isGenerating = false,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const currentModel = getModelById(selectedModel);

  const getModelIcon = (modelId: string) => {
    if (modelId.includes('pro')) return <Cpu size={14} />;
    if (modelId.includes('flash-lite')) return <Zap size={14} />;
    return <Gauge size={14} />;
  };

  const getModelColor = (modelId: string) => {
    if (modelId.includes('pro')) return 'text-purple-600 dark:text-purple-400';
    if (modelId.includes('flash-lite')) return 'text-green-600 dark:text-green-400';
    return 'text-blue-600 dark:text-blue-400';
  };

  const handleModelSelect = (modelId: string) => {
    onModelChange(modelId);
    setIsOpen(false);
  };

  return (
    <div className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        disabled={disabled || isGenerating}
        className={`
          flex items-center gap-2 px-2 py-1 rounded-lg border transition-colors
          ${disabled || isGenerating
            ? 'bg-gray-100 dark:bg-gray-800 text-gray-400 cursor-not-allowed'
            : 'bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300'
          }
          border-gray-300 dark:border-gray-600 text-sm min-w-[200px]
          ${isGenerating ? 'animate-pulse' : ''}
        `}
      >
        <div className={`flex items-center gap-2 ${getModelColor(selectedModel)}`}>
          {getModelIcon(selectedModel)}
          <span className="font-medium">
            {currentModel?.name || 'Unknown Model'}
          </span>
          {isGenerating && (
            <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" />
          )}
        </div>
        <ChevronDown 
          size={16} 
          className={`ml-auto transition-transform ${isOpen ? 'rotate-180' : ''}`} 
        />
      </button>

      {isOpen && !disabled && !isGenerating && (
        <>
          {/* Backdrop */}
          <div 
            className="fixed inset-0 z-10" 
            onClick={() => setIsOpen(false)}
          />
          
          {/* Dropdown */}
          <div className="absolute top-full left-0 mt-1 w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg z-20">
            {GEMINI_MODELS.map((model) => (
              <button
                key={model.id}
                onClick={() => handleModelSelect(model.id)}
                className={`
                  w-full px-3 py-3 text-left hover:bg-gray-50 dark:hover:bg-gray-700 
                  transition-colors first:rounded-t-lg last:rounded-b-lg
                  ${selectedModel === model.id ? 'bg-blue-50 dark:bg-blue-900/20' : ''}
                `}
              >
                <div className="flex items-center gap-2 mb-1">
                  <div className={getModelColor(model.id)}>
                    {getModelIcon(model.id)}
                  </div>
                  <span className="text-xs font-medium text-gray-900 dark:text-gray-100">
                    {model.name}
                  </span>
                  {model.supportsSearchGrounding && (
                    <Search size={12} className="text-blue-500 dark:text-blue-400" aria-label="Supports search grounding" />
                  )}
                  {selectedModel === model.id && (
                    <div className="ml-auto w-2 h-2 bg-blue-500 rounded-full" />
                  )}
                </div>
                <div className="text-[10px] text-gray-500 dark:text-gray-400 ml-6">
                  {model.description}
                </div>
              </button>
            ))}
          </div>
        </>
      )}
    </div>
  );
};
