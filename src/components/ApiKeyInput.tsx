import React, { useState, useEffect } from 'react';
import { KeyRound } from 'lucide-react';

interface ApiKeyInputProps {
  apiKey: string;
  onSetApiKey: (key: string) => void;
}

export const ApiKeyInput: React.FC<ApiKeyInputProps> = ({ apiKey, onSetApiKey }) => {
  const [localApiKey, setLocalApiKey] = useState(apiKey);
  const [isKeySaved, setIsKeySaved] = useState(false);

  useEffect(() => {
    setLocalApiKey(apiKey);
  }, [apiKey]);

  const handleApiKeySave = () => {
    onSetApiKey(localApiKey);
    setIsKeySaved(true);
    setTimeout(() => setIsKeySaved(false), 2000);
  };

  return (
    <div className="flex items-center gap-2">
      <KeyRound size={16} className="text-gray-600 dark:text-gray-400" />
      <input
        type="password"
        placeholder="Gemini API Key"
        className="bg-transparent text-sm w-40 focus:outline-none text-gray-700 dark:text-gray-300 placeholder-gray-400 dark:placeholder-gray-500"
        value={localApiKey}
        onChange={(e) => setLocalApiKey(e.target.value)}
        onKeyDown={(e) => e.key === 'Enter' && handleApiKeySave()}
      />
      <button
        onClick={handleApiKeySave}
        className={`text-sm px-2 py-1 rounded-md transition-colors ${isKeySaved ? 'bg-green-500' : 'bg-blue-500 hover:bg-blue-600'} text-white`}
        disabled={isKeySaved}
      >
        {isKeySaved ? 'Saved' : 'Save'}
      </button>
    </div>
  );
};