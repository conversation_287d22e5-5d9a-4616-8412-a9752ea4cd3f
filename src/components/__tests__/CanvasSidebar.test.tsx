import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import { vi } from 'vitest';
import { MindMap } from '../MindMap';

// Mock the hooks
vi.mock('../../hooks/useMindMap', () => ({
  useMindMap: () => ({
    data: {
      nodes: {
        'test-node': {
          id: 'test-node',
          title: 'Test Node',
          content: '',
          x: 0,
          y: 0,
          childIds: [],
          parentId: null,
          isExpanded: true,
          query: '',
          isSearchGrounding: false,
        }
      },
      rootNodeId: 'test-node',
      searchGrounding: false,
      selectedModel: 'gemini-2.0-flash-exp',
      version: 1,
      lastModified: Date.now(),
    },
    viewport: { x: 0, y: 0, zoom: 1 },
    setViewport: vi.fn(),
    updateNode: vi.fn(),
    setNodeQuery: vi.fn(),
    clearNodeQuery: vi.fn(),
    createChildNode: vi.fn(),
    deleteNode: vi.fn(),
    toggleNodeSearchGrounding: vi.fn(),
    selectNode: vi.fn(),
    exportData: vi.fn(),
    importData: vi.fn(),
    apiKey: '',
    setApiKey: vi.fn(),
    selectedModel: 'gemini-2.0-flash-exp',
    setSelectedModel: vi.fn(),
    canvasManager: {
      canvases: {
        'test-canvas': {
          id: 'test-canvas',
          name: 'Test Canvas',
          data: {
            nodes: {},
            rootNodeId: 'test-node',
            searchGrounding: false,
            selectedModel: 'gemini-2.0-flash-exp',
            version: 1,
            lastModified: Date.now(),
          },
          viewport: { x: 0, y: 0, zoom: 1 },
          createdAt: Date.now(),
          lastModified: Date.now(),
        }
      },
      activeCanvasId: 'test-canvas',
      switchCanvas: vi.fn(),
      createCanvas: vi.fn(),
      renameCanvas: vi.fn(),
      deleteCanvas: vi.fn(),
      duplicateCanvas: vi.fn(),
    },
  }),
}));

vi.mock('../../hooks/useTheme', () => ({
  useTheme: () => ({
    theme: 'light',
    toggleTheme: vi.fn(),
  }),
}));

// Mock localStorage
const localStorageMock = (() => {
  let store: Record<string, string> = {};

  return {
    getItem: (key: string) => store[key] || null,
    setItem: (key: string, value: string) => {
      store[key] = value.toString();
    },
    removeItem: (key: string) => {
      delete store[key];
    },
    clear: () => {
      store = {};
    },
  };
})();

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

describe('CanvasSidebar Persistent State', () => {
  beforeEach(() => {
    localStorageMock.clear();
  });

  it('should initialize sidebar as expanded by default', () => {
    render(<MindMap />);
    
    // Check if sidebar is expanded (should show "WonderMap" title)
    expect(screen.getByText('WonderMap')).toBeInTheDocument();
  });

  it('should save collapsed state to localStorage when toggled', () => {
    render(<MindMap />);
    
    // Find and click the collapse button
    const collapseButton = screen.getByTitle('Collapse sidebar');
    fireEvent.click(collapseButton);
    
    // Check if localStorage was updated
    expect(localStorageMock.getItem('canvasSidebarCollapsed')).toBe('true');
  });

  it('should restore collapsed state from localStorage on mount', () => {
    // Set localStorage to collapsed state
    localStorageMock.setItem('canvasSidebarCollapsed', 'true');
    
    render(<MindMap />);
    
    // Check if sidebar is collapsed (should not show "WonderMap" title)
    expect(screen.queryByText('WonderMap')).not.toBeInTheDocument();
    
    // Check if expand button is shown
    expect(screen.getByTitle('Expand sidebar')).toBeInTheDocument();
  });

  it('should restore expanded state from localStorage on mount', () => {
    // Set localStorage to expanded state
    localStorageMock.setItem('canvasSidebarCollapsed', 'false');
    
    render(<MindMap />);
    
    // Check if sidebar is expanded (should show "WonderMap" title)
    expect(screen.getByText('WonderMap')).toBeInTheDocument();
    
    // Check if collapse button is shown
    expect(screen.getByTitle('Collapse sidebar')).toBeInTheDocument();
  });

  it('should handle invalid localStorage data gracefully', () => {
    // Set invalid JSON in localStorage
    localStorageMock.setItem('canvasSidebarCollapsed', 'invalid-json');
    
    // Should not throw error and default to expanded
    render(<MindMap />);
    
    // Check if sidebar defaults to expanded
    expect(screen.getByText('WonderMap')).toBeInTheDocument();
  });

  it('should toggle between collapsed and expanded states', () => {
    render(<MindMap />);
    
    // Initially expanded
    expect(screen.getByText('WonderMap')).toBeInTheDocument();
    expect(localStorageMock.getItem('canvasSidebarCollapsed')).toBe('false');
    
    // Click to collapse
    const collapseButton = screen.getByTitle('Collapse sidebar');
    fireEvent.click(collapseButton);
    
    // Should be collapsed
    expect(screen.queryByText('WonderMap')).not.toBeInTheDocument();
    expect(localStorageMock.getItem('canvasSidebarCollapsed')).toBe('true');
    
    // Click to expand
    const expandButton = screen.getByTitle('Expand sidebar');
    fireEvent.click(expandButton);
    
    // Should be expanded again
    expect(screen.getByText('WonderMap')).toBeInTheDocument();
    expect(localStorageMock.getItem('canvasSidebarCollapsed')).toBe('false');
  });
});
