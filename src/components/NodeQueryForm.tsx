import React, { useState } from 'react';
import { Send, Loader2, Globe } from 'lucide-react';
import { NodeData } from '../types';

interface NodeQueryFormProps {
  node: NodeData;
  nodes: Record<string, NodeData>;
  isGeneratingResponse: boolean;
  onSubmit: (query: string) => void;
}

export const NodeQueryForm: React.FC<NodeQueryFormProps> = ({ node, nodes, isGeneratingResponse, onSubmit }) => {
  const [queryInput, setQueryInput] = useState(node.query || '');
  const textareaRef = React.useRef<HTMLTextAreaElement>(null);

  // Sync queryInput with node.query prop changes
  React.useEffect(() => {
    setQueryInput(node.query || '');
  }, [node.query]);

  React.useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }
  }, [queryInput]);

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && (e.metaKey || e.ctrlKey)) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!queryInput.trim() || isGeneratingResponse) return;
    onSubmit(queryInput.trim());
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-3">
      {node.parentId && nodes[node.parentId]?.hasQueried && (
        <div className="bg-purple-50 dark:bg-purple-600/20 border border-purple-200 dark:border-purple-600/30 rounded-lg p-2">
          <div className="text-xs text-purple-700 dark:text-purple-400 font-medium mb-1">
            📎 Will use parent context from "{nodes[node.parentId].title}"
          </div>
          <div className="text-xs text-gray-600 dark:text-gray-300 truncate">
            {nodes[node.parentId].query}
          </div>
        </div>
      )}
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Ask AI a question:
        </label>
        <textarea
          ref={textareaRef}
          value={queryInput}
          onChange={(e) => setQueryInput(e.target.value)}
          onKeyDown={handleKeyDown}
          placeholder="What would you like to know about this topic?"
          disabled={isGeneratingResponse}
          className="w-full bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-lg px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 focus:border-blue-500 focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed resize-none overflow-hidden"
          rows={3}
        />
        <div className="text-xs text-gray-500 dark:text-gray-500 mt-1">
          Press {navigator.platform.includes('Mac') ? 'Cmd' : 'Ctrl'} + Enter to submit
        </div>
      </div>
      <div className="flex items-center justify-between mt-3">
        <div className="flex items-center gap-2">
          {node.searchGrounding && (
            <div className="flex items-center gap-1 text-xs text-blue-600 dark:text-blue-400">
              <Globe size={12} />
              Search enabled
            </div>
          )}
        </div>
        <button
          type="submit"
          disabled={!queryInput.trim() || isGeneratingResponse}
          className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 dark:disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded-lg px-4 py-2 text-sm transition-colors flex items-center gap-2"
        >
          {isGeneratingResponse ? (
            <>
              <Loader2 size={16} className="animate-spin" />
              Thinking...
            </>
          ) : (
            <>
              <Send size={16} />
              Ask AI
            </>
          )}
        </button>
      </div>
    </form>
  );
};