import React, { useState } from 'react';
import { ChevronUp, ChevronDown, HelpCircle, Globe } from 'lucide-react';

export const QuickGuide: React.FC = () => {
  const [isInstructionsExpanded, setIsInstructionsExpanded] = useState(false);

  return (
    <div className="relative">
      <button
        className="flex items-center gap-2 p-1 hover:bg-gray-100 dark:hover:bg-gray-700/50 rounded-md transition-colors text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white"
        onClick={() => setIsInstructionsExpanded(!isInstructionsExpanded)}
        title="Toggle Quick Guide"
      >
        <HelpCircle size={16} />
      </button>

      {isInstructionsExpanded && (
        <div className="absolute top-full right-0 mt-2 bg-white/90 dark:bg-gray-900/90 backdrop-blur-sm rounded-lg border border-gray-200/50 dark:border-gray-700/50 shadow-lg z-50 transition-all duration-300 w-[320px]">
          <div
            className="flex items-center justify-between p-3 cursor-pointer hover:bg-gray-50/50 dark:hover:bg-gray-800/50 rounded-t-lg transition-colors"
            onClick={() => setIsInstructionsExpanded(!isInstructionsExpanded)}
          >
            <div className="flex items-center gap-2">
              <HelpCircle size={16} className="text-gray-600 dark:text-gray-400" />
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Quick Guide
              </span>
            </div>
            <button className="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 transition-colors">
              {isInstructionsExpanded ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
            </button>
          </div>

          <div className="px-3 pb-3 border-t border-gray-200/50 dark:border-gray-700/50 pt-3">
            <div className="text-xs text-gray-600 dark:text-gray-400 space-y-2 whitespace-nowrap">
              <div className="flex items-start gap-2">
                <span className="text-blue-600 dark:text-blue-400 font-medium">•</span>
                <span>First add your <a href="https://aistudio.google.com/app/apikey" target="_blank" rel="noopener noreferrer" className="text-blue-600 dark:text-blue-400 hover:underline">Gemini API Key</a> in the toolbar</span>
              </div>
              <div className="flex items-start gap-2">
                <span className="text-purple-600 dark:text-purple-400 font-medium">•</span>
                <span>Select your preferred AI model in the toolbar</span>
              </div>
              <div className="flex items-start gap-2">
                <span className="text-indigo-600 dark:text-indigo-400 font-medium">•</span>
                <span>Use + button to create child nodes</span>
              </div>
              <div className="flex items-start gap-2">
                <span className="text-teal-600 dark:text-teal-400 font-medium">•</span>
                <span>Toggle 🔍 for search grounding per node</span>
              </div>
              <div className="border-t border-gray-200/50 dark:border-gray-700/50 pt-1 mt-1"></div>
              <div className="flex items-start gap-2">
                <Globe size={12} className="text-gray-600 dark:text-gray-400 mt-0.5" />
                <span>Visit <a href="https://bagusfarisa.com" target="_blank" rel="noopener noreferrer" className="text-blue-600 dark:text-blue-400 hover:underline font-medium">bagusfarisa.com</a> for more projects</span>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
